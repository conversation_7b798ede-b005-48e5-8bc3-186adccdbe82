/* FontAwesome 图标简化版本 */

.fa, .fas, .far, .fal, .fab {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands";
    font-weight: 900;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}

.fab {
    font-weight: 400;
}

/* 常用图标 */
.fa-phone::before { content: "📞"; }
.fa-envelope::before { content: "✉️"; }
.fa-map-marker-alt::before { content: "📍"; }
.fa-globe::before { content: "🌐"; }
.fa-user::before { content: "👤"; }
.fa-eye::before { content: "👁️"; }
.fa-calendar::before { content: "📅"; }
.fa-book::before { content: "📚"; }
.fa-user-graduate::before { content: "🎓"; }
.fa-graduation-cap::before { content: "🎓"; }
.fa-users::before { content: "👥"; }
.fa-microscope::before { content: "🔬"; }
.fa-chevron-left::before { content: "‹"; }
.fa-chevron-right::before { content: "›"; }
.fa-chevron-up::before { content: "^"; }

/* 社交媒体图标 */
.fa-facebook::before { content: "f"; }
.fa-twitter::before { content: "t"; }
.fa-instagram::before { content: "i"; }
.fa-youtube::before { content: "y"; }
.fa-linkedin::before { content: "in"; }

/* 图标大小 */
.fa-xs { font-size: 0.75em; }
.fa-sm { font-size: 0.875em; }
.fa-lg { font-size: 1.33333em; line-height: 0.75em; vertical-align: -0.0667em; }
.fa-xl { font-size: 1.5em; line-height: 0.6667em; vertical-align: -0.075em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
.fa-4x { font-size: 4em; }
.fa-5x { font-size: 5em; }

/* 图标动画 */
.fa-spin {
    animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 固定宽度图标 */
.fa-fw {
    text-align: center;
    width: 1.25em;
}
