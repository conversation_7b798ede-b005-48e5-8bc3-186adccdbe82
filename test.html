<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        img {
            max-width: 200px;
            height: auto;
            margin: 10px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <h1>网站诊断测试页面</h1>
    
    <div class="test-section">
        <h2>1. CSS 文件加载测试</h2>
        <p>如果这个文本是黑色且有正确的字体，CSS 加载正常。</p>
        <link rel="stylesheet" href="assets/css/bootstrap.min.css">
        <link rel="stylesheet" href="assets/css/fontawesome.min.css">
        <link rel="stylesheet" href="assets/css/style.css">
        <p class="success">CSS 文件已加载</p>
    </div>

    <div class="test-section">
        <h2>2. 本地图片加载测试</h2>
        <img src="assets/images/logo.svg" alt="Logo" onerror="this.style.border='2px solid red'; this.alt='Logo 加载失败';">
        <img src="assets/images/hero1.png" alt="Hero1" onerror="this.style.border='2px solid red'; this.alt='Hero1 加载失败';">
        <img src="assets/images/class.png" alt="Class" onerror="this.style.border='2px solid red'; this.alt='Class 加载失败';">
    </div>

    <div class="test-section">
        <h2>3. 外部图片加载测试</h2>
        <img src="https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" 
             alt="Unsplash Test" 
             onerror="this.style.border='2px solid red'; this.alt='外部图片加载失败';">
    </div>

    <div class="test-section">
        <h2>4. JavaScript 测试</h2>
        <button onclick="testJS()">点击测试 JavaScript</button>
        <p id="js-result"></p>
    </div>

    <div class="test-section">
        <h2>5. Bootstrap 测试</h2>
        <div class="btn btn-primary">Bootstrap 按钮</div>
        <div class="alert alert-info mt-2">Bootstrap 警告框</div>
    </div>

    <script src="assets/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        function testJS() {
            document.getElementById('js-result').innerHTML = '<span class="success">JavaScript 工作正常！</span>';
        }

        // 检查文件加载状态
        window.addEventListener('load', function() {
            console.log('页面加载完成');
            
            // 检查 CSS
            const styles = window.getComputedStyle(document.body);
            console.log('Body font-family:', styles.fontFamily);
            
            // 检查图片
            const images = document.querySelectorAll('img');
            images.forEach((img, index) => {
                img.addEventListener('load', function() {
                    console.log(`图片 ${index + 1} 加载成功:`, img.src);
                });
                img.addEventListener('error', function() {
                    console.error(`图片 ${index + 1} 加载失败:`, img.src);
                });
            });
        });

        // 检查错误
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
            document.body.innerHTML += '<div class="test-section"><h2 class="error">发现错误:</h2><p>' + e.message + '</p></div>';
        });
    </script>
</body>
</html>
