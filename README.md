# 吉尔吉斯斯坦北岭大学官网
## Northern Foothills University of Kyrgyzstan

这是一个仿照吉尔吉斯斯坦马纳斯大学官网设计的现代化大学网站前端项目。

### 项目特点

- 🎨 现代化响应式设计
- 🌍 多语言支持（中文、英文、吉尔吉斯语、俄语）
- 📱 移动端友好
- ⚡ 轻量级纯前端实现
- 🎯 用户体验优化

### 技术栈

- **HTML5** - 语义化标记
- **CSS3** - 现代样式和动画
- **JavaScript (ES6+)** - 交互功能
- **Bootstrap 5** - 响应式布局
- **FontAwesome** - 图标库
- **SVG** - 矢量图形

### 项目结构

```
northern-foothills-university/
├── index.html              # 主页
├── package.json            # 项目配置
├── README.md              # 项目说明
└── assets/                # 静态资源
    ├── css/               # 样式文件
    │   ├── style.css      # 主样式
    │   ├── bootstrap.min.css
    │   └── fontawesome.min.css
    ├── js/                # JavaScript文件
    │   ├── main.js        # 主要功能
    │   └── bootstrap.bundle.min.js
    └── images/            # 图片资源
        ├── logo.svg       # 主logo
        ├── logo-white.svg # 白色logo
        ├── hero1.svg      # 轮播图
        ├── news1.svg      # 新闻图片1
        ├── news2.svg      # 新闻图片2
        └── news3.svg      # 新闻图片3
```

### 主要功能

#### 🏠 首页功能
- **英雄轮播图** - 自动播放的图片轮播
- **快速链接** - 四个主要功能入口
- **统计数据** - 动态数字计数动画
- **新闻展示** - 最新新闻和公告
- **响应式导航** - 多级下拉菜单

#### 🎨 设计特色
- **现代化UI** - 简洁美观的界面设计
- **渐变色彩** - 蓝色主题配色方案
- **动画效果** - 平滑的过渡和悬停效果
- **卡片布局** - 信息模块化展示

#### 📱 响应式设计
- **桌面端** - 完整功能展示
- **平板端** - 适配中等屏幕
- **手机端** - 移动优化布局

### 快速开始

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd northern-foothills-university
   ```

2. **安装依赖**
   ```bash
   npm install
   ```

3. **启动开发服务器**
   ```bash
   npm run dev
   ```

4. **访问网站**
   打开浏览器访问 `http://localhost:3000`

### 开发说明

#### 自定义样式
主要样式文件位于 `assets/css/style.css`，包含：
- CSS变量定义
- 组件样式
- 响应式媒体查询
- 动画效果

#### JavaScript功能
主要功能文件位于 `assets/js/main.js`，包含：
- 轮播图控制
- 数字计数动画
- 平滑滚动
- 语言切换
- 返回顶部

#### 图片资源
所有图片使用SVG格式，确保：
- 矢量缩放不失真
- 文件体积小
- 加载速度快
- 支持样式定制

### 浏览器支持

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+

### 部署

#### 静态部署
项目为纯前端项目，可直接部署到任何静态托管服务：
- GitHub Pages
- Netlify
- Vercel
- 阿里云OSS
- 腾讯云COS

#### 部署步骤
1. 将所有文件上传到服务器
2. 确保 `index.html` 为入口文件
3. 配置服务器支持SPA路由（如需要）

### 定制指南

#### 修改颜色主题
在 `assets/css/style.css` 中修改CSS变量：
```css
:root {
    --primary-color: #1e3a8a;    /* 主色调 */
    --secondary-color: #3b82f6;  /* 辅助色 */
    --accent-color: #f59e0b;     /* 强调色 */
}
```

#### 添加新页面
1. 创建新的HTML文件
2. 复制导航和页脚结构
3. 添加页面特定内容
4. 更新导航链接

#### 修改内容
- 文字内容直接在HTML中修改
- 图片替换对应的SVG文件
- 链接地址在相应标签中更新

### 许可证

MIT License - 详见 LICENSE 文件

### 贡献

欢迎提交 Issue 和 Pull Request 来改进项目。

### 联系方式

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 网站：www.nfu.edu.kg

---

© 2025 吉尔吉斯斯坦北岭大学. 保留所有权利.
