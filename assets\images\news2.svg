<svg width="400" height="250" viewBox="0 0 400 250" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="newsGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="400" height="250" fill="url(#newsGradient2)"/>
  
  <!-- 学生帽 -->
  <path d="M200 80 L160 100 L200 120 L240 100 Z" fill="white"/>
  <rect x="195" y="75" width="10" height="15" fill="white"/>
  <circle cx="200" cy="75" r="3" fill="#d97706"/>
  
  <!-- 书本 -->
  <rect x="150" y="140" width="100" height="60" fill="white" opacity="0.9" rx="3"/>
  <rect x="155" y="145" width="90" height="50" fill="#1e3a8a" opacity="0.8" rx="2"/>
  <rect x="160" y="150" width="80" height="3" fill="white"/>
  <rect x="160" y="160" width="70" height="2" fill="white"/>
  <rect x="160" y="170" width="75" height="2" fill="white"/>
  
  <!-- 标题文字 -->
  <text x="200" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white">2025年招生启动</text>
  <text x="200" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white" opacity="0.9">Admission 2025</text>
</svg>
