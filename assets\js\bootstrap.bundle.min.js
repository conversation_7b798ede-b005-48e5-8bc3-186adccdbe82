// Bootstrap 5 JavaScript 简化版本

(function() {
    'use strict';

    // 下拉菜单功能
    function initDropdowns() {
        const dropdownToggles = document.querySelectorAll('[data-bs-toggle="dropdown"]');
        
        dropdownToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                const menu = this.nextElementSibling;
                if (menu && menu.classList.contains('dropdown-menu')) {
                    // 关闭其他下拉菜单
                    document.querySelectorAll('.dropdown-menu.show').forEach(openMenu => {
                        if (openMenu !== menu) {
                            openMenu.classList.remove('show');
                        }
                    });
                    
                    // 切换当前下拉菜单
                    menu.classList.toggle('show');
                }
            });
        });
        
        // 点击外部关闭下拉菜单
        document.addEventListener('click', function() {
            document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        });
    }

    // 折叠导航栏功能
    function initNavbarToggle() {
        const toggles = document.querySelectorAll('[data-bs-toggle="collapse"]');
        
        toggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const targetId = this.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                
                if (target) {
                    target.classList.toggle('show');
                }
            });
        });
    }

    // 模态框功能
    function initModals() {
        const modalToggles = document.querySelectorAll('[data-bs-toggle="modal"]');
        
        modalToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('data-bs-target');
                const modal = document.querySelector(targetId);
                
                if (modal) {
                    modal.style.display = 'block';
                    modal.classList.add('show');
                    document.body.classList.add('modal-open');
                }
            });
        });
        
        // 关闭模态框
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal') || e.target.getAttribute('data-bs-dismiss') === 'modal') {
                const modal = e.target.closest('.modal') || e.target;
                if (modal && modal.classList.contains('modal')) {
                    modal.style.display = 'none';
                    modal.classList.remove('show');
                    document.body.classList.remove('modal-open');
                }
            }
        });
    }

    // 工具提示功能
    function initTooltips() {
        const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                const title = this.getAttribute('title') || this.getAttribute('data-bs-title');
                if (title) {
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip fade show bs-tooltip-top';
                    tooltip.innerHTML = `
                        <div class="tooltip-arrow"></div>
                        <div class="tooltip-inner">${title}</div>
                    `;
                    
                    document.body.appendChild(tooltip);
                    
                    const rect = this.getBoundingClientRect();
                    tooltip.style.position = 'absolute';
                    tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
                    tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
                    tooltip.style.zIndex = '1070';
                    
                    this._tooltip = tooltip;
                }
            });
            
            element.addEventListener('mouseleave', function() {
                if (this._tooltip) {
                    this._tooltip.remove();
                    this._tooltip = null;
                }
            });
        });
    }

    // 标签页功能
    function initTabs() {
        const tabToggles = document.querySelectorAll('[data-bs-toggle="tab"]');
        
        tabToggles.forEach(toggle => {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                
                const targetId = this.getAttribute('data-bs-target') || this.getAttribute('href');
                const target = document.querySelector(targetId);
                
                if (target) {
                    // 移除所有活动状态
                    const tabContainer = this.closest('.nav-tabs') || this.closest('.nav-pills');
                    if (tabContainer) {
                        tabContainer.querySelectorAll('.nav-link').forEach(link => {
                            link.classList.remove('active');
                        });
                    }
                    
                    document.querySelectorAll('.tab-pane').forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });
                    
                    // 激活当前标签
                    this.classList.add('active');
                    target.classList.add('show', 'active');
                }
            });
        });
    }

    // 轮播图功能
    function initCarousels() {
        const carousels = document.querySelectorAll('.carousel');
        
        carousels.forEach(carousel => {
            let currentSlide = 0;
            const slides = carousel.querySelectorAll('.carousel-item');
            const indicators = carousel.querySelectorAll('.carousel-indicators button');
            const prevBtn = carousel.querySelector('.carousel-control-prev');
            const nextBtn = carousel.querySelector('.carousel-control-next');
            
            if (slides.length === 0) return;
            
            function showSlide(index) {
                slides.forEach((slide, i) => {
                    slide.classList.toggle('active', i === index);
                });
                
                indicators.forEach((indicator, i) => {
                    indicator.classList.toggle('active', i === index);
                });
                
                currentSlide = index;
            }
            
            function nextSlide() {
                currentSlide = (currentSlide + 1) % slides.length;
                showSlide(currentSlide);
            }
            
            function prevSlide() {
                currentSlide = (currentSlide - 1 + slides.length) % slides.length;
                showSlide(currentSlide);
            }
            
            if (nextBtn) {
                nextBtn.addEventListener('click', nextSlide);
            }
            
            if (prevBtn) {
                prevBtn.addEventListener('click', prevSlide);
            }
            
            indicators.forEach((indicator, index) => {
                indicator.addEventListener('click', () => showSlide(index));
            });
            
            // 自动播放
            if (carousel.getAttribute('data-bs-ride') === 'carousel') {
                setInterval(nextSlide, 5000);
            }
        });
    }

    // 折叠面板功能
    function initAccordions() {
        const accordionButtons = document.querySelectorAll('[data-bs-toggle="collapse"]');
        
        accordionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const targetId = this.getAttribute('data-bs-target');
                const target = document.querySelector(targetId);
                const parent = this.getAttribute('data-bs-parent');
                
                if (target) {
                    // 如果有父容器，关闭其他面板
                    if (parent) {
                        const parentElement = document.querySelector(parent);
                        if (parentElement) {
                            parentElement.querySelectorAll('.collapse.show').forEach(collapse => {
                                if (collapse !== target) {
                                    collapse.classList.remove('show');
                                }
                            });
                        }
                    }
                    
                    target.classList.toggle('show');
                }
            });
        });
    }

    // 初始化所有组件
    function init() {
        initDropdowns();
        initNavbarToggle();
        initModals();
        initTooltips();
        initTabs();
        initCarousels();
        initAccordions();
    }

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // 导出到全局
    window.bootstrap = {
        init: init
    };

})();
